import { openai } from "@ai-sdk/openai";
import { Agent, createTool } from "@convex-dev/agent";
import { z } from "zod";
import { api } from "./_generated/api";

// This will be available after running `npx convex dev`
// For now, we'll create a placeholder
const components = {
  agent: {} as any,
};

// Create the brand management agent
export const brandAgent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  textEmbedding: openai.embedding("text-embedding-3-small"),
  instructions: `
    You are a helpful brand management assistant that helps users process and analyze brand websites.

    Your primary functions are:
    - Process brand URLs to extract content and generate embeddings
    - Search through processed brand data using semantic search
    - Provide insights about brands based on their website content
    - Help users understand brand positioning, messaging, and key information

    When responding:
    - Always confirm the URL format before processing
    - Provide clear status updates during brand processing
    - Use semantic search to find relevant brand information
    - Keep responses informative and actionable
    - If processing fails, explain the issue clearly

    Use the available tools to process brand URLs and search existing brand data.
  `,
  tools: {
    processBrandUrl: createTool({
      description: "Process a brand URL to extract content and generate embeddings for RAG system",
      args: z.object({
        url: z.string().url().describe("The brand website URL to process"),
      }),
      handler: async (ctx, args): Promise<string> => {
        try {
          // Validate URL format
          new URL(args.url);
          
          // Add brand using Convex mutation
          const brandId = await ctx.runMutation(api.brands.addBrand, { url: args.url });
          
          return `Brand URL ${args.url} has been successfully added and queued for processing. Brand ID: ${brandId}. The system will extract content and generate embeddings automatically.`;
        } catch (error) {
          if (error instanceof Error && error.message.includes("already exists")) {
            return `Brand URL ${args.url} has already been added to the system.`;
          }
          return `Error processing brand URL: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      },
    }),

    searchBrands: createTool({
      description: "Search through processed brand data using semantic search",
      args: z.object({
        query: z.string().describe("The search query to find relevant brand information"),
        limit: z.number().min(1).max(20).default(5).describe("Maximum number of results to return"),
      }),
      handler: async (ctx, args): Promise<string> => {
        try {
          // Search brands using Convex action
          const results = await ctx.runAction(api.brands.searchBrands, { 
            query: args.query, 
            limit: args.limit 
          });

          if (results.length === 0) {
            return `No brand results found for query: "${args.query}". Make sure brands have been processed and try different search terms.`;
          }

          let response = `Found ${results.length} relevant brand results for query: "${args.query}"\n\n`;
          
          results.forEach((result: any, index: number) => {
            response += `${index + 1}. **${result.brandName || result.brandUrl}**\n`;
            response += `   URL: ${result.brandUrl}\n`;
            response += `   Content: ${result.content.substring(0, 200)}...\n`;
            response += `   Relevance Score: ${result.relevanceScore.toFixed(3)}\n\n`;
          });

          return response;
        } catch (error) {
          return `Error searching brand data: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      },
    }),

    getBrandStatus: createTool({
      description: "Get the processing status of a brand by URL",
      args: z.object({
        url: z.string().url().describe("The brand website URL to check status for"),
      }),
      handler: async (ctx, args): Promise<string> => {
        try {
          // Get brand status using Convex query
          const brand = await ctx.runQuery(api.brands.getBrandByUrl, { url: args.url });

          if (!brand) {
            return `Brand URL ${args.url} not found in the system. Use the processBrandUrl tool to add it.`;
          }

          let response = `Brand Status for ${args.url}:\n`;
          response += `- Status: ${brand.status}\n`;
          response += `- Name: ${brand.name || 'Not extracted yet'}\n`;
          response += `- Added: ${new Date(brand.createdAt).toLocaleDateString()}\n`;
          response += `- Last Updated: ${new Date(brand.updatedAt).toLocaleDateString()}\n`;
          
          if (brand.status === 'failed' && brand.errorMessage) {
            response += `- Error: ${brand.errorMessage}\n`;
          }

          return response;
        } catch (error) {
          return `Error getting brand status: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      },
    }),

    listAllBrands: createTool({
      description: "List all brands in the system with their current status",
      args: z.object({}),
      handler: async (ctx): Promise<string> => {
        try {
          // Get all brands using Convex query
          const brands = await ctx.runQuery(api.brands.listBrands);

          if (brands.length === 0) {
            return "No brands found in the system. Use the processBrandUrl tool to add some brands.";
          }

          let response = `Found ${brands.length} brands in the system:\n\n`;
          
          brands.forEach((brand: any, index: number) => {
            response += `${index + 1}. **${brand.name || brand.url}**\n`;
            response += `   URL: ${brand.url}\n`;
            response += `   Status: ${brand.status}\n`;
            response += `   Added: ${new Date(brand.createdAt).toLocaleDateString()}\n`;
            if (brand.status === 'failed' && brand.errorMessage) {
              response += `   Error: ${brand.errorMessage}\n`;
            }
            response += '\n';
          });

          // Add summary statistics
          const statusCounts = brands.reduce((acc: Record<string, number>, brand: any) => {
            acc[brand.status] = (acc[brand.status] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);

          response += `**Summary:**\n`;
          Object.entries(statusCounts).forEach(([status, count]) => {
            response += `- ${status}: ${count}\n`;
          });

          return response;
        } catch (error) {
          return `Error listing brands: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      },
    }),
  },
  maxSteps: 3,
  maxRetries: 2,
});

// Export actions for the agent
export const createThread = brandAgent.createThreadMutation();
export const generateText = brandAgent.asTextAction();
export const saveMessages = brandAgent.asSaveMessagesMutation();
