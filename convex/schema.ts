import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

// The schema is normally optional, but Convex Auth
// requires indexes defined on `authTables`.
// The schema provides more precise TypeScript types.
export default defineSchema({
  ...authTables,
  // Brand management tables
  brands: defineTable({
    url: v.string(),
    name: v.optional(v.string()),
    status: v.union(v.literal("pending"), v.literal("processing"), v.literal("completed"), v.literal("failed")),
    createdAt: v.number(),
    updatedAt: v.number(),
    errorMessage: v.optional(v.string()),
  }).index("byUrl", ["url"]).index("byStatus", ["status"]),

  brand_content: defineTable({
    brandId: v.id("brands"),
    rawContent: v.string(),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    extractedAt: v.number(),
  }).index("byBrandId", ["brandId"]),

  brand_chunks: defineTable({
    brandId: v.id("brands"),
    contentId: v.id("brand_content"),
    text: v.string(),
    chunkIndex: v.number(),
    embeddingId: v.union(v.id("brand_embeddings"), v.null()),
  }).index("byBrandId", ["brandId"])
    .index("byContentId", ["contentId"])
    .index("byEmbeddingId", ["embeddingId"]),

  brand_embeddings: defineTable({
    chunkId: v.id("brand_chunks"),
    embedding: v.array(v.number()),
    model: v.string(),
    createdAt: v.number(),
  }).index("byChunkId", ["chunkId"])
    .vectorIndex("byEmbedding", {
      vectorField: "embedding",
      dimensions: 1536, // OpenAI text-embedding-ada-002 dimensions
    }),
});
