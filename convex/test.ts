import { query, action, internalQuery, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import { v } from "convex/values";

// Test function to verify the brand management system
export const testBrandSystem = action({
  args: {},
  handler: async (ctx) => {
    console.log("🧪 Testing Brand Management System...");

    // Test 1: Check if schema is properly set up
    try {
      const brands = await ctx.runQuery(internal.test.getBrandsForTest);
      console.log("✅ Schema test passed - brands table accessible");
      console.log(`📊 Current brands count: ${brands.length}`);
    } catch (error) {
      console.log("❌ Schema test failed:", error);
      return { success: false, error: "Schema not properly configured" };
    }

    // Test 2: Check if we can insert a test brand
    try {
      const testBrandId = await ctx.runMutation(internal.test.insertTestBrand);
      console.log("✅ Insert test passed - can create brands");

      // Clean up test data
      await ctx.runMutation(internal.test.deleteTestBrand, { brandId: testBrandId });
      console.log("🧹 Test data cleaned up");
    } catch (error) {
      console.log("❌ Insert test failed:", error);
      return { success: false, error: "Cannot insert brands" };
    }

    // Test 3: Check if vector index is set up
    try {
      // This will fail gracefully if vector index isn't ready
      await ctx.vectorSearch("brand_embeddings", "byEmbedding", {
        vector: new Array(1536).fill(0), // Dummy vector
        limit: 1,
      });
      console.log("✅ Vector search test passed - index is ready");
    } catch (error) {
      console.log("⚠️ Vector search not ready yet (this is normal on first run)");
    }

    console.log("🎉 Brand Management System tests completed!");
    return {
      success: true,
      message: "Brand management system is properly configured and ready to use!"
    };
  },
});

// Helper function to get system status
export const getSystemStatus = query({
  args: {},
  handler: async (ctx) => {
    const brands = await ctx.db.query("brands").collect();
    const content = await ctx.db.query("brand_content").collect();
    const chunks = await ctx.db.query("brand_chunks").collect();
    const embeddings = await ctx.db.query("brand_embeddings").collect();

    const statusCounts = brands.reduce((acc, brand) => {
      acc[brand.status] = (acc[brand.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalBrands: brands.length,
      totalContent: content.length,
      totalChunks: chunks.length,
      totalEmbeddings: embeddings.length,
      statusBreakdown: statusCounts,
      systemHealth: {
        database: "✅ Connected",
        schema: "✅ Configured",
        vectorIndex: embeddings.length > 0 ? "✅ Active" : "⚠️ No embeddings yet",
      }
    };
  },
});

// Helper functions for testing
export const getBrandsForTest = internalQuery({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("brands").collect();
  },
});

export const insertTestBrand = internalMutation({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.insert("brands", {
      url: "https://test-brand.example.com",
      status: "pending",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

export const deleteTestBrand = internalMutation({
  args: {
    brandId: v.id("brands"),
  },
  handler: async (ctx, { brandId }) => {
    await ctx.db.delete(brandId);
  },
});
