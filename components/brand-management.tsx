"use client";

import { useState } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../convex/_generated/api";
import { Doc } from "../convex/_generated/dataModel";
import { BrandChatInterface } from "./brand-chat";

export function BrandManagementComponent() {
  const [url, setUrl] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [message, setMessage] = useState("");

  // Query to get all brands
  const brands = useQuery(api.brands.listBrands);

  // Mutation to add a new brand
  const addBrand = useMutation(api.brands.addBrand);

  // Action to retry failed brands
  const retryFailedBrands = useAction(api.brands.retryFailedBrands);

  // Action to retry a single brand
  const processBrand = useAction(api.brands.processBrand);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!url.trim()) {
      setMessage("Please enter a valid URL");
      return;
    }

    setIsSubmitting(true);
    setMessage("");

    try {
      // Validate URL format
      new URL(url);

      await addBrand({ url: url.trim() });
      setUrl("");
      setMessage("Brand URL added successfully and queued for processing!");
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes("Invalid URL")) {
          setMessage("Please enter a valid URL format");
        } else if (error.message.includes("already exists")) {
          setMessage("This brand URL has already been added");
        } else {
          setMessage(`Error: ${error.message}`);
        }
      } else {
        setMessage("An unexpected error occurred");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRetryFailed = async () => {
    setIsRetrying(true);
    setMessage("");

    try {
      const result = await retryFailedBrands();
      setMessage(`Successfully retried ${result.retriedCount} failed brand(s)!`);
    } catch (error) {
      if (error instanceof Error) {
        setMessage(`Error retrying failed brands: ${error.message}`);
      } else {
        setMessage("An unexpected error occurred while retrying");
      }
    } finally {
      setIsRetrying(false);
    }
  };

  const handleRetryIndividual = async (brandId: string) => {
    setMessage("");

    try {
      await processBrand({ brandId });
      setMessage("Brand processing restarted successfully!");
    } catch (error) {
      if (error instanceof Error) {
        setMessage(`Error retrying brand: ${error.message}`);
      } else {
        setMessage("An unexpected error occurred while retrying");
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600 bg-green-100";
      case "processing":
        return "text-blue-600 bg-blue-100";
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "failed":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return "✓";
      case "processing":
        return "⟳";
      case "pending":
        return "⏳";
      case "failed":
        return "✗";
      default:
        return "?";
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* URL Input Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
          Add Brand URL
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Brand Website URL
            </label>
            <input
              type="url"
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              disabled={isSubmitting}
            />
          </div>
          <div className="flex items-center gap-4">
            <button
              type="submit"
              disabled={isSubmitting || !url.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Processing..." : "Add Brand"}
            </button>
            {message && (
              <p className={`text-sm ${message.includes("Error") || message.includes("already") ? "text-red-600" : "text-green-600"}`}>
                {message}
              </p>
            )}
          </div>
        </form>
      </div>

      {/* Total Brands Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Total Brands
          </h2>
          {brands && brands.some((brand: Doc<"brands">) => brand.status === "failed") && (
            <button
              onClick={handleRetryFailed}
              disabled={isRetrying}
              className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isRetrying ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Retrying...
                </>
              ) : (
                <>
                  <span>🔄</span>
                  Retry Failed
                </>
              )}
            </button>
          )}
        </div>
        
        {brands === undefined ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading brands...</span>
          </div>
        ) : brands.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No brands added yet. Add your first brand URL above!
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Brand URL
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Added
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Last Updated
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {brands.map((brand: Doc<"brands">) => (
                  <tr key={brand._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <a
                        href={brand.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
                      >
                        {brand.url}
                      </a>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {brand.name || "—"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(brand.status)}`}>
                        <span className="mr-1">{getStatusIcon(brand.status)}</span>
                        {brand.status}
                      </span>
                      {brand.status === "failed" && brand.errorMessage && (
                        <div className="mt-1 text-xs text-red-600 dark:text-red-400">
                          {brand.errorMessage}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(brand.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(brand.updatedAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {brand.status === "failed" && (
                        <button
                          onClick={() => handleRetryIndividual(brand._id)}
                          className="px-3 py-1 bg-orange-600 text-white text-xs rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-1"
                        >
                          Retry
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Statistics */}
      {brands && brands.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {["pending", "processing", "completed", "failed"].map((status) => {
            const count = brands.filter((brand: Doc<"brands">) => brand.status === status).length;
            return (
              <div key={status} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div className="flex items-center">
                  <div className={`p-2 rounded-full ${getStatusColor(status)}`}>
                    {getStatusIcon(status)}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 capitalize">
                      {status}
                    </p>
                    <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {count}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Brand Management Assistant */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
          Brand Management Assistant
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Chat with the AI assistant to process brand URLs, search through brand data, and get insights about your brands.
        </p>
        <BrandChatInterface />
      </div>
    </div>
  );
}
